"""
数据库连接和模型定义
"""
import os
import logging
from typing import Optional, List, Dict, Any
from sqlalchemy import create_engine, Column, Integer, String, Text, DateTime, Boolean, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError
from datetime import datetime

# 导入settings配置
try:
    from backend.config import settings
except ImportError:
    # 如果相对导入失败，使用绝对路径导入
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent.parent))
    from backend.config import settings

logger = logging.getLogger(__name__)

Base = declarative_base()

class Article(Base):
    """文章模型"""
    __tablename__ = "articles"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False, index=True)
    content = Column(Text, nullable=True)
    summary = Column(Text, nullable=True)
    author = Column(String(100), nullable=True)
    category = Column(String(100), nullable=True)
    tags = Column(String(500), nullable=True)
    status = Column(String(20), default="published")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    published_at = Column(DateTime, nullable=True)
    is_deleted = Column(Boolean, default=False)
    source_url = Column(String(500), nullable=True)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "title": self.title,
            "content": self.content,
            "summary": self.summary,
            "author": self.author,
            "category": self.category,
            "tags": self.tags,
            "status": self.status,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "published_at": self.published_at.isoformat() if self.published_at else None,
            "is_deleted": self.is_deleted,
            "source_url": self.source_url
        }

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.engine = None
        self.SessionLocal = None
        self._connected = False
    
    def connect(self, database_url: str) -> bool:
        """连接数据库"""
        try:
            logger.info(f"正在尝试连接数据库: {database_url.replace(database_url.split('@')[0].split('//')[1], '***:***')}")

            self.engine = create_engine(
                database_url,
                pool_pre_ping=True,
                pool_recycle=300,
                echo=False,
                connect_args={
                    "connect_timeout": 10,
                    "charset": "utf8mb4"
                }
            )

            logger.info("数据库引擎创建成功，正在测试连接...")

            # 测试连接
            with self.engine.connect() as conn:
                result = conn.execute(text("SELECT 1 as test"))
                test_value = result.fetchone()[0]
                logger.info(f"数据库连接测试成功，返回值: {test_value}")

            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )

            self._connected = True
            logger.info("数据库连接和会话工厂创建成功")
            return True

        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            logger.error(f"错误类型: {type(e).__name__}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            self._connected = False
            return False
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self._connected
    
    def get_session(self) -> Optional[Session]:
        """获取数据库会话"""
        if not self._connected or not self.SessionLocal:
            return None
        return self.SessionLocal()
    
    def close(self):
        """关闭数据库连接"""
        if self.engine:
            self.engine.dispose()
        self._connected = False
        logger.info("数据库连接已关闭")

# 全局数据库管理器实例
db_manager = DatabaseManager()

def get_database_url() -> Optional[str]:
    """获取数据库连接URL"""
    # 从settings配置中获取数据库信息
    host = settings.db_host or "localhost"
    port = settings.db_port or "3306"
    user = settings.db_user or "root"
    password = settings.db_password or ""
    database = settings.db_name or "chestnut_cms"

    logger.info(f"数据库配置信息:")
    logger.info(f"  主机: {host}")
    logger.info(f"  端口: {port}")
    logger.info(f"  用户: {user}")
    logger.info(f"  密码: {'***' if password else '(未设置)'}")
    logger.info(f"  数据库: {database}")

    if not password:
        logger.warning("数据库密码未设置")
        return None

    if not user:
        logger.warning("数据库用户未设置")
        return None

    return f"mysql+pymysql://{user}:{password}@{host}:{port}/{database}?charset=utf8mb4"

def init_database() -> bool:
    """初始化数据库连接"""
    logger.info("开始初始化数据库连接...")

    # 检查是否安装了pymysql
    try:
        import pymysql
        logger.info("PyMySQL模块已安装")
    except ImportError as e:
        logger.error(f"PyMySQL模块未安装: {e}")
        logger.error("请运行: pip install pymysql")
        return False

    database_url = get_database_url()
    if not database_url:
        logger.warning("数据库配置不完整，跳过数据库初始化")
        return False

    logger.info("数据库配置完整，开始连接...")
    success = db_manager.connect(database_url)

    if success:
        logger.info("数据库初始化成功")
    else:
        logger.error("数据库初始化失败")

    return success

def get_db_session() -> Optional[Session]:
    """获取数据库会话的依赖函数"""
    return db_manager.get_session()
